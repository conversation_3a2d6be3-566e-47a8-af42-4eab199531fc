'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/Internal/Button'
import { Download, Eye, Code, ChevronLeft, ChevronRight } from 'lucide-react'
import { useTaskStore } from '@/store/task'
import { downloadFile } from '@/utils/file'

type ViewMode = 'code' | 'preview'

export default function HtmlPreview() {
  const htmlContent = useTaskStore((state) => state.htmlContent)
  const [viewMode, setViewMode] = useState<ViewMode>('code')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [cleanedHtmlContent, setCleanedHtmlContent] = useState('')
  const [isOutputComplete, setIsOutputComplete] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const contentCheckTimer = useRef<NodeJS.Timeout | null>(null)
  const previousContentLength = useRef(0)

  // 清理 HTML 内容，去除 ```html 和 ``` 标记
  useEffect(() => {
    if (htmlContent) {
      let cleaned = htmlContent
      // 去除开头的 ```html 标记（包括可能的空白字符）
      cleaned = cleaned.replace(/^[\s\S]*?```html\s*/i, '')
      // 去除结尾的 ``` 标记（包括可能的空白字符）
      cleaned = cleaned.replace(/\s*```[\s\S]*$/, '')
      // 去除首尾空白字符
      cleaned = cleaned.trim()
      setCleanedHtmlContent(cleaned)
    } else {
      setCleanedHtmlContent('')
    }
  }, [htmlContent])

  // 检测HTML输出是否完成
  useEffect(() => {
    if (!htmlContent) {
      setIsOutputComplete(false)
      return
    }

    // 清除之前的定时器
    if (contentCheckTimer.current) {
      clearTimeout(contentCheckTimer.current)
    }

    // 检查内容长度是否变化
    const currentLength = htmlContent.length
    const hasContentChanged = currentLength !== previousContentLength.current
    previousContentLength.current = currentLength

    if (hasContentChanged) {
      // 内容还在变化，重置完成状态
      setIsOutputComplete(false)
      if (htmlContent.includes('</html>')) {
        // 设置新的定时器检查内容是否稳定
        contentCheckTimer.current = setTimeout(() => {
          setIsOutputComplete(true)
          // 输出完成后自动切换到预览模式
          setViewMode('preview')
        }, 2000) // 2秒内没有变化则认为输出完成
      }
    }
    return () => {
      if (contentCheckTimer.current) {
        clearTimeout(contentCheckTimer.current)
      }
    }
  }, [htmlContent])

  // 在预览模式下更新 iframe 内容
  useEffect(() => {
    if (viewMode === 'preview' && iframeRef.current && cleanedHtmlContent) {
      const iframe = iframeRef.current
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (iframeDoc) {
        iframeDoc.open()
        iframeDoc.write(cleanedHtmlContent)
        iframeDoc.close()

        // 添加基础样式以改善显示效果
        const style = iframeDoc.createElement('style')
        style.textContent = `
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
          }
          h1, h2, h3, h4, h5, h6 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
          }
          p {
            margin: 0.8em 0;
          }
          pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
          }
          code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
          }
          table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: #f8f9fa;
          }
        `
        iframeDoc.head.appendChild(style)
      }
    }
  }, [cleanedHtmlContent, viewMode])

  // 当侧边栏收起或展开时，动态添加或移除样式
  useEffect(() => {
    if (isCollapsed) {
      // 侧边栏收起时，移除样式

      const existingStyle = document.getElementById('html-preview-style')
      if (existingStyle) {
        document.head.removeChild(existingStyle)
      }
    } else if (htmlContent) {
      // 侧边栏展开且有内容时，添加样式
      let style = document.getElementById('html-preview-style')
      if (!style) {
        style = document.createElement('style')
        style.id = 'html-preview-style'
        style.innerHTML = `
          .main-box {
            height: 100vh;
            margin: 0;
            padding: 80px;
          }
        `
        document.head.appendChild(style)
      }
    }

    // 清理函数
    return () => {
      // 只有当侧边栏收起时才移除样式
      if (isCollapsed) {
        const existingStyle = document.getElementById('html-preview-style')
        if (existingStyle) {
          document.head.removeChild(existingStyle)
        }
      }
    }
  }, [isCollapsed, htmlContent])

  const handleDownload = () => {
    if (cleanedHtmlContent) {
      downloadFile(cleanedHtmlContent, 'report.html', 'text/html')
    }
  }

  const toggleViewMode = () => {
    setViewMode((prevMode) => (prevMode === 'code' ? 'preview' : 'code'))
  }

  return (
    <div
      className={`fixed right-0 top-0 h-full bg-white border-l shadow-lg flex z-50 transition-all duration-300 ease-in-out ${
        isCollapsed ? 'w-8' : 'w-[47%]'
      }`}>
      {/* 收起/展开按钮 */}
      <div className="h-full flex items-center justify-center bg-gray-100 hover:bg-gray-200 cursor-pointer">
        <Button
          variant="ghost"
          size="icon"
          className="h-full rounded-none"
          onClick={() => setIsCollapsed(!isCollapsed)}>
          {isCollapsed ? (
            <ChevronLeft className="w-4 h-4" />
          ) : (
            <ChevronRight className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* 主要内容区域 - 始终渲染但通过CSS控制显示 */}
      <div
        className={`flex-1 flex flex-col transition-opacity duration-300 w-0 ${
          isCollapsed ? 'opacity-0 pointer-events-none ' : 'opacity-100'
        }`}>
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold text-lg">HTML 预览</h3>
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'code' ? 'default' : 'outline'}
              size="sm"
              onClick={toggleViewMode}
              title="查看代码">
              <Code className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'preview' ? 'default' : 'outline'}
              size="sm"
              onClick={toggleViewMode}
              title="HTML 预览">
              <Eye className="w-4 h-4" />
            </Button>
            <Button size="sm" onClick={handleDownload} title="下载 HTML">
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          {viewMode === 'code' ? (
            <div className="h-full relative">
              <pre className="h-full p-4 overflow-auto bg-gray-50 text-sm whitespace-pre-wrap break-words">
                {cleanedHtmlContent || (
                  <span className="text-gray-400">等待HTML内容输出...</span>
                )}
              </pre>
              {isOutputComplete && (
                <div className="absolute bottom-4 right-4 bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs">
                  输出完成
                </div>
              )}
            </div>
          ) : (
            <iframe
              ref={iframeRef}
              className="w-full h-full border-none"
              title="HTML Preview"
              sandbox="allow-scripts allow-same-origin allow-popups"
              loading="lazy"
            />
          )}
        </div>
      </div>
    </div>
  )
}